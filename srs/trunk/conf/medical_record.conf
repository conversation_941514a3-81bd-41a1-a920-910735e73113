# 医疗录制系统专用SRS配置
listen 1935;
max_connections 1000;
daemon off;
srs_log_tank console;

# HTTP API
http_api {
    enabled on;
    listen 1985;
    crossdomain on;
}

# HTTP服务器
http_server {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}

# WebRTC服务器
rtc_server {
    enabled on;
    listen 8000;
    candidate $CANDIDATE;
    reuseport 4;
    use_auto_detect_network_ip on;
    api_as_candidates on;
    resolve_api_domain on;
    ecdsa on;
    encrypt on;
}

# 默认虚拟主机
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled on;
        rtmp_to_rtc on;
        rtc_to_rtmp on;
        stun_timeout 30;
        nack on;
        twcc on;
    }
    
    # 录制配置
    dvr {
        enabled on;
        dvr_path ./recordings/[app]/[stream].[timestamp].mp4;
        dvr_plan session;
        dvr_duration 30;
        dvr_wait_keyframe on;
    }
    
    # HTTP回调
    http_hooks {
        enabled on;
        on_dvr http://localhost:5000/api/hooks/on_dvr;
        on_publish http://localhost:5000/api/hooks/on_publish;
        on_unpublish http://localhost:5000/api/hooks/on_unpublish;
    }
    
    # HTTP-FLV
    http_remux {
        enabled on;
        mount [vhost]/[app]/[stream].flv;
    }
    
    # HLS
    hls {
        enabled on;
        hls_fragment 10;
        hls_window 60;
        hls_path ./objs/nginx/html/[app]/;
        hls_m3u8_file [stream].m3u8;
        hls_ts_file [stream]-[seq].ts;
    }
}
