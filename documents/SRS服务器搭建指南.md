# SRS 6.0 服务器搭建指南 - 支持WebRTC并发推流

## 目录
- [1. 项目结构分析](#1-项目结构分析)
- [2. 编译环境准备](#2-编译环境准备)
- [3. 编译配置](#3-编译配置)
- [4. WebRTC配置详解](#4-webrtc配置详解)
- [5. 并发优化配置](#5-并发优化配置)
- [6. 部署步骤](#6-部署步骤)
- [7. 服务启动与测试](#7-服务启动与测试)
- [8. 故障排除](#8-故障排除)

## 1. 项目结构分析

### 1.1 SRS项目目录结构
```
/data/MedicalRecord/srs/
├── trunk/                    # 主要源码目录
│   ├── conf/                # 配置文件目录
│   │   ├── full.conf       # 完整配置文件（参考）
│   │   ├── rtc.conf        # WebRTC专用配置
│   │   ├── docker.conf     # Docker部署配置
│   │   └── ...
│   ├── src/                # 源代码
│   ├── objs/               # 编译输出目录
│   ├── scripts/            # 脚本工具
│   │   └── install.sh      # 安装脚本
│   ├── configure           # 配置脚本
│   └── Makefile           # 编译文件
├── Dockerfile             # Docker构建文件
└── README.md             # 项目说明
```

### 1.2 关键配置文件分析

#### rtc.conf - WebRTC基础配置
- **用途**: WebRTC推流的最小化配置
- **特点**: 配置简洁，适合快速测试
- **candidate**: 使用环境变量 `$CANDIDATE`

#### docker.conf - Docker部署配置  
- **用途**: 容器化部署的标准配置
- **特点**: 包含完整的WebRTC和RTMP转换功能
- **日志**: 使用console输出，适合Docker日志管理

#### full.conf - 完整功能配置
- **用途**: 包含所有功能的完整配置参考
- **特点**: 详细的配置说明和选项
- **适用**: 生产环境定制化配置

## 2. 编译环境准备

### 2.1 系统要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **编译器**: GCC 4.8+ 或 Clang 3.5+
- **内存**: 至少2GB RAM
- **磁盘**: 至少5GB可用空间

### 2.2 依赖安装

#### Ubuntu/Debian系统
```bash
# 更新包管理器
sudo apt update

# 安装编译依赖
sudo apt install -y gcc g++ make git unzip

# 安装可选依赖（用于完整功能）
sudo apt install -y tcl expect lsb-release
```

#### CentOS/RHEL系统
```bash
# 安装编译依赖
sudo yum install -y gcc gcc-c++ make git unzip

# 安装可选依赖
sudo yum install -y tcl expect redhat-lsb-core
```

### 2.3 网络环境检查
```bash
# 检查网络接口
ifconfig

# 获取本机IP地址（用于candidate配置）
# Ubuntu/Debian
CANDIDATE=$(ifconfig eth0 | grep 'inet ' | awk '{print $2}')

# CentOS 7
CANDIDATE=$(ifconfig eth0 | grep 'inet ' | awk '{print $2}')

# 通用方法
CANDIDATE=$(hostname -I | awk '{print $1}')

echo "本机IP: $CANDIDATE"
```

## 3. 编译配置

### 3.1 配置编译选项
```bash
# 进入源码目录
cd /data/MedicalRecord/srs/trunk

# 查看可用配置选项
./configure --help

# 推荐的WebRTC编译配置
./configure \
    --rtc=on \
    --srt=on \
    --gb28181=on \
    --h265=on \
    --utest=off \
    --gperf=off
```

### 3.2 编译选项说明
| 选项 | 说明 | 推荐值 | 备注 |
|------|------|--------|------|
| --rtc | WebRTC支持 | on | **必须开启** |
| --srt | SRT协议支持 | on | 增强传输可靠性 |
| --gb28181 | 国标28181支持 | on | 监控设备接入 |
| --h265 | H.265编码支持 | on | 高效视频编码 |
| --utest | 单元测试 | off | 生产环境关闭 |
| --gperf | 性能分析工具 | off | 调试时开启 |

### 3.3 执行编译
```bash
# 开始编译（使用多核加速）
make -j$(nproc)

# 检查编译结果
ls -la objs/srs

# 验证WebRTC功能
./objs/srs -t
```

## 4. WebRTC配置详解

### 4.1 rtc_server全局配置

#### 基础配置
```conf
rtc_server {
    # 启用WebRTC服务器
    enabled on;
    
    # UDP监听端口（WebRTC媒体传输）
    listen 8000;
    
    # 服务器候选地址（关键配置）
    candidate $CANDIDATE;
    
    # 协议选择：udp, tcp, all
    protocol udp;
    
    # IP族选择：ipv4, ipv6, all
    ip_family ipv4;
}
```

#### 并发优化配置
```conf
rtc_server {
    # 启用端口复用（提高并发能力）
    reuseport 4;
    
    # 启用自动IP检测
    use_auto_detect_network_ip on;
    
    # API作为候选地址
    api_as_candidates on;
    
    # 域名解析
    resolve_api_domain on;
    
    # ECDSA证书（性能更好）
    ecdsa on;
    
    # SRTP加密
    encrypt on;
}
```

#### TCP传输支持（可选）
```conf
rtc_server {
    # TCP传输配置（UDP不通时的备选方案）
    tcp {
        enabled on;
        listen 8000;
    }
    protocol all;  # 同时支持UDP和TCP
}
```

### 4.2 vhost虚拟主机配置

#### 基础WebRTC配置
```conf
vhost __defaultVhost__ {
    rtc {
        # 启用WebRTC
        enabled on;
        
        # RTMP转WebRTC（用于播放）
        rtmp_to_rtc on;
        
        # WebRTC转RTMP（用于推流录制）
        rtc_to_rtmp on;
        
        # 会话超时时间（秒）
        stun_timeout 30;
        
        # 启用NACK（丢包重传）
        nack on;
        
        # 启用TWCC（拥塞控制）
        twcc on;
    }
}
```

#### 录制配置（支持自动录制）
```conf
vhost __defaultVhost__ {
    # DVR录制配置
    dvr {
        enabled on;
        dvr_path ./objs/nginx/html/[app]/[stream].[timestamp].mp4;
        dvr_plan session;
        dvr_duration 30;
        dvr_wait_keyframe on;
    }
    
    # HTTP回调配置
    http_hooks {
        enabled on;
        on_dvr http://localhost:5000/api/hooks/on_dvr;
        on_publish http://localhost:5000/api/hooks/on_publish;
        on_unpublish http://localhost:5000/api/hooks/on_unpublish;
    }
}
```

### 4.3 Candidate配置详解

#### 配置方式优先级
1. **URL参数指定** (最高优先级)
   ```
   http://localhost:1985/rtc/v1/whip/?eip=*************:8000
   ```

2. **环境变量配置**
   ```bash
   export CANDIDATE="*************"
   ./objs/srs -c conf/rtc.conf
   ```

3. **配置文件直接指定**
   ```conf
   candidate *************;
   ```

4. **自动检测** (最低优先级)
   ```conf
   candidate *;
   ```

#### 网络环境适配
```bash
# 内网环境
CANDIDATE=$(hostname -I | awk '{print $1}')

# 公网环境（云服务器）
CANDIDATE=$(curl -s http://checkip.amazonaws.com)

# 指定网卡
CANDIDATE=$(ifconfig eth0 | grep 'inet ' | awk '{print $2}')

# Docker环境
CANDIDATE=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' container_name)
```

## 5. 并发优化配置

### 5.1 系统级优化

#### 内核参数调优
```bash
# 创建系统优化脚本
cat > /tmp/srs_system_optimize.sh << 'EOF'
#!/bin/bash

# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 网络参数优化
cat >> /etc/sysctl.conf << 'SYSCTL'
# SRS WebRTC优化参数
net.core.rmem_max = 134217728
net.core.rmem_default = 65536
net.core.wmem_max = 134217728
net.core.wmem_default = 65536
net.core.netdev_max_backlog = 5000
net.ipv4.udp_mem = 102400 873800 16777216
net.ipv4.udp_rmem_min = 8192
net.ipv4.udp_wmem_min = 8192
SYSCTL

# 应用配置
sysctl -p
EOF

# 执行优化
sudo bash /tmp/srs_system_optimize.sh
```

### 5.2 SRS服务配置优化

#### 并发连接配置
```conf
# 全局配置
listen 1935;
max_connections 2000;  # 最大连接数
daemon off;            # 前台运行（便于监控）

# 工作线程配置
work_dir ./;
pid ./objs/srs.pid;

# 日志配置
srs_log_tank console;
srs_log_level trace;
```

#### 性能监控配置
```conf
# 统计信息
stats {
    network 0;
    disk sda vda xvda xvdb;
}

# HTTP API
http_api {
    enabled on;
    listen 1985;
    crossdomain on;
}

# HTTP服务器
http_server {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}
```

### 5.3 多进程部署方案

#### 负载均衡配置
```bash
# 创建多实例配置
for i in {1..4}; do
    cp conf/rtc.conf conf/rtc_${i}.conf
    sed -i "s/listen 1985/listen $((1985+i))/g" conf/rtc_${i}.conf
    sed -i "s/listen 8080/listen $((8080+i))/g" conf/rtc_${i}.conf
    sed -i "s/listen 8000/listen $((8000+i))/g" conf/rtc_${i}.conf
done
```

#### Nginx负载均衡
```nginx
upstream srs_api {
    server 127.0.0.1:1986;
    server 127.0.0.1:1987;
    server 127.0.0.1:1988;
    server 127.0.0.1:1989;
}

server {
    listen 1985;
    location / {
        proxy_pass http://srs_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 6. 部署步骤

### 6.1 创建专用配置文件
```bash
# 创建医疗录制专用配置
cat > conf/medical_record.conf << 'EOF'
# 医疗录制系统专用SRS配置
listen 1935;
max_connections 1000;
daemon off;
srs_log_tank console;

# HTTP API
http_api {
    enabled on;
    listen 1985;
    crossdomain on;
}

# HTTP服务器
http_server {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}

# WebRTC服务器
rtc_server {
    enabled on;
    listen 8000;
    candidate $CANDIDATE;
    reuseport 4;
    use_auto_detect_network_ip on;
    api_as_candidates on;
    resolve_api_domain on;
    ecdsa on;
    encrypt on;
}

# 默认虚拟主机
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled on;
        rtmp_to_rtc on;
        rtc_to_rtmp on;
        stun_timeout 30;
        nack on;
        twcc on;
    }
    
    # 录制配置
    dvr {
        enabled on;
        dvr_path ./recordings/[app]/[stream].[timestamp].mp4;
        dvr_plan session;
        dvr_duration 30;
        dvr_wait_keyframe on;
    }
    
    # HTTP回调
    http_hooks {
        enabled on;
        on_dvr http://localhost:5000/api/hooks/on_dvr;
        on_publish http://localhost:5000/api/hooks/on_publish;
        on_unpublish http://localhost:5000/api/hooks/on_unpublish;
    }
    
    # HTTP-FLV
    http_remux {
        enabled on;
        mount [vhost]/[app]/[stream].flv;
    }
    
    # HLS
    hls {
        enabled on;
        hls_fragment 10;
        hls_window 60;
        hls_path ./objs/nginx/html/[app]/;
        hls_m3u8_file [stream].m3u8;
        hls_ts_file [stream]-[seq].ts;
    }
}
EOF
```

### 6.2 创建录制目录
```bash
# 创建录制文件存储目录
mkdir -p recordings/live
mkdir -p objs/nginx/html/live

# 设置权限
chmod 755 recordings
chmod 755 objs/nginx/html
```

### 6.3 创建启动脚本
```bash
# 创建启动脚本
cat > start_srs.sh << 'EOF'
#!/bin/bash

# 获取本机IP作为candidate
CANDIDATE=$(hostname -I | awk '{print $1}')
if [ -z "$CANDIDATE" ]; then
    CANDIDATE="127.0.0.1"
fi

echo "使用Candidate IP: $CANDIDATE"

# 导出环境变量
export CANDIDATE=$CANDIDATE

# 启动SRS
./objs/srs -c conf/medical_record.conf
EOF

chmod +x start_srs.sh
```

## 7. 服务启动与测试

### 7.1 启动SRS服务
```bash
# 方式1：使用启动脚本
./start_srs.sh

# 方式2：直接启动
export CANDIDATE=$(hostname -I | awk '{print $1}')
./objs/srs -c conf/medical_record.conf

# 方式3：后台运行
nohup ./start_srs.sh > srs.log 2>&1 &
```

### 7.2 服务状态检查
```bash
# 检查进程状态
ps aux | grep srs

# 检查端口监听
netstat -tulpn | grep -E "(1935|1985|8080|8000)"

# 检查API状态
curl http://localhost:1985/api/v1/versions

# 检查WebRTC API
curl http://localhost:1985/api/v1/rtc/
```

### 7.3 WebRTC推流测试

#### 使用浏览器测试
1. **打开测试页面**
   ```
   http://localhost:8080/players/rtc_publisher.html
   ```

2. **配置推流参数**
   ```javascript
   // 推流URL
   const url = 'webrtc://localhost:8000/live/test_stream';

   // 或使用API方式
   const apiUrl = 'http://localhost:1985/rtc/v1/whip/?app=live&stream=test_stream';
   ```

#### 使用FFmpeg推流测试
```bash
# RTMP推流（转换为WebRTC）
ffmpeg -re -i test_video.mp4 -c copy -f flv rtmp://localhost:1935/live/test_stream

# 验证WebRTC播放
# 浏览器访问: http://localhost:8080/players/rtc_player.html?stream=test_stream
```

### 7.4 并发测试

#### 压力测试脚本
```bash
# 创建并发测试脚本
cat > concurrent_test.sh << 'EOF'
#!/bin/bash

CONCURRENT_COUNT=${1:-10}
SERVER_IP=${2:-localhost}

echo "开始 $CONCURRENT_COUNT 路并发推流测试..."

for i in $(seq 1 $CONCURRENT_COUNT); do
    {
        echo "启动推流 $i"
        ffmpeg -re -f lavfi -i testsrc=duration=60:size=640x480:rate=25 \
               -f lavfi -i sine=frequency=1000:duration=60 \
               -c:v libx264 -preset ultrafast -tune zerolatency \
               -c:a aac -f flv \
               rtmp://$SERVER_IP:1935/live/stream_$i \
               > /dev/null 2>&1
    } &
done

wait
echo "并发测试完成"
EOF

chmod +x concurrent_test.sh

# 执行测试
./concurrent_test.sh 10 localhost
```

## 8. 故障排除

### 8.1 常见问题诊断

#### WebRTC连接失败
```bash
# 检查candidate配置
echo "当前CANDIDATE: $CANDIDATE"

# 检查防火墙
sudo ufw status
sudo iptables -L

# 检查UDP端口
sudo netstat -ulpn | grep 8000

# 测试网络连通性
nc -u localhost 8000
```

#### 推流失败排查
```bash
# 检查RTMP端口
telnet localhost 1935

# 查看SRS日志
tail -f objs/logs/srs.log

# 检查API响应
curl -v http://localhost:1985/api/v1/streams/
```

#### 录制文件问题
```bash
# 检查录制目录权限
ls -la recordings/

# 检查磁盘空间
df -h

# 查看录制回调日志
curl http://localhost:1985/api/v1/dvrs/
```

### 8.2 性能优化建议

#### 系统资源监控
```bash
# CPU使用率
top -p $(pgrep srs)

# 内存使用
ps aux | grep srs | awk '{print $6}'

# 网络流量
iftop -i eth0

# 文件描述符使用
lsof -p $(pgrep srs) | wc -l
```

#### 配置调优建议
1. **高并发场景**
   - 增加 `max_connections` 到 2000+
   - 设置 `reuseport` 为 CPU核心数
   - 调整系统UDP缓冲区大小

2. **低延迟场景**
   - 启用 `realtime` 模式
   - 减少 `dvr_duration`
   - 优化网络参数

3. **稳定性优化**
   - 启用 `daemon` 模式
   - 配置日志轮转
   - 设置进程监控

### 8.3 监控和告警

#### 健康检查脚本
```bash
cat > health_check.sh << 'EOF'
#!/bin/bash

# 检查SRS进程
if ! pgrep srs > /dev/null; then
    echo "ERROR: SRS进程未运行"
    exit 1
fi

# 检查API响应
if ! curl -s http://localhost:1985/api/v1/versions > /dev/null; then
    echo "ERROR: SRS API无响应"
    exit 1
fi

# 检查WebRTC端口
if ! nc -z -u localhost 8000; then
    echo "ERROR: WebRTC端口8000不可用"
    exit 1
fi

echo "OK: SRS服务运行正常"
exit 0
EOF

chmod +x health_check.sh
```

#### 自动重启脚本
```bash
cat > auto_restart.sh << 'EOF'
#!/bin/bash

while true; do
    if ! ./health_check.sh; then
        echo "$(date): SRS服务异常，正在重启..."
        pkill srs
        sleep 5
        ./start_srs.sh &
        sleep 30
    fi
    sleep 60
done
EOF

chmod +x auto_restart.sh
```

### 8.4 Docker部署方案

#### Dockerfile优化
```dockerfile
FROM ubuntu:20.04

# 安装依赖
RUN apt-get update && apt-get install -y \
    gcc g++ make git unzip \
    && rm -rf /var/lib/apt/lists/*

# 复制源码
COPY srs /usr/local/srs
WORKDIR /usr/local/srs/trunk

# 编译SRS
RUN ./configure --rtc=on --srt=on --gb28181=on --h265=on \
    && make -j$(nproc)

# 暴露端口
EXPOSE 1935 1985 8080 8000/udp

# 启动命令
CMD ["./objs/srs", "-c", "conf/medical_record.conf"]
```

#### Docker Compose配置
```yaml
version: '3.8'
services:
  srs:
    build: .
    ports:
      - "1935:1935"
      - "1985:1985"
      - "8080:8080"
      - "8000:8000/udp"
    volumes:
      - ./recordings:/usr/local/srs/trunk/recordings
      - ./logs:/usr/local/srs/trunk/objs/logs
    environment:
      - CANDIDATE=${CANDIDATE}
    restart: unless-stopped
```

## 9. 生产环境部署清单

### 9.1 部署前检查
- [ ] 服务器硬件配置满足要求
- [ ] 网络环境配置正确
- [ ] 防火墙规则已配置
- [ ] 系统参数已优化
- [ ] 存储空间充足

### 9.2 配置文件检查
- [ ] candidate配置正确
- [ ] 录制路径可写
- [ ] 回调URL可访问
- [ ] 端口无冲突
- [ ] 日志配置合理

### 9.3 测试验证
- [ ] 单路推流测试通过
- [ ] 并发推流测试通过
- [ ] 录制功能正常
- [ ] 回调接口正常
- [ ] 监控告警正常

---

**文档版本**: v1.0
**创建时间**: 2025-01-08
**适用版本**: SRS 6.0
**维护人**: 开发团队
